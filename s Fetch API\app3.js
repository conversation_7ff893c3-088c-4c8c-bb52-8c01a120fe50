//Axios

//updating Query strings  (url)

const url="http://universities.hipolabs.com/search?name="
let btn=document.querySelector("button")
 btn.addEventListener("click",async()=>{
    let country=document.querySelector("input").value
    let colleges= await getColleges(country);
    let para=document.querySelector("#result");
    for(const d of colleges){
        para.innerText+=d.name;
        para.innerHTML+="<br>";
    }
    // para.innerText=colleges;
 })

 async function getColleges(country){
    try{
        let res=await axios.get(url+country)
        console.log(res.data);
        return res.data;
    }catch(err){
        console.log("error : ",err);
        return [];
    }
 }