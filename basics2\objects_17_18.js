// const tinderUser = new Object()  
const tinderUser = {}

tinderUser.id = "123abc"
tinderUser.name = "<PERSON>"
tinderUser.isLoggedIn = false

console.log(tinderUser);

const regularUser = {
    email: "<EMAIL>",
    fullname: {
        userfullname: {
            firstname: "hitesh",
            lastname: "choudhary"
        }
    }
}
console.log(regularUser);

console.log(regularUser.fullname.userfullname.firstname);

const obj1 = {1: "a", 2: "b"}
const obj2 = {3: "a", 4: "b"}
const obj4 = {5: "a", 6: "b"}

//concatenation-->

// const obj3 = { obj1, obj2 } //objects in objects not concatnation
// const obj3 = Object.assign({}, obj1, obj2, obj4)

const obj3 = {...obj1, ...obj2}
console.log(obj3);


const users = [
    {
        id: 1,
        email: "<EMAIL>"
    },
    {
        id: 1,
        email: "<EMAIL>"
    },
    {
        id: 1,
        email: "<EMAIL>"
    },
]

users[1].email
// console.log(tinderUser);

console.log(Object.keys(tinderUser)); //array of keys
console.log(Object.values(tinderUser)); //array of values
console.log(Object.entries(tinderUser)); //array of array , makes easy key value pair an array

//checking if the  object has this key or not
console.log(tinderUser.hasOwnProperty('isLoggedIn')); 



//*********************************************** */

//object de-structure-->

//basic syntax:
const object1 = {
  property1: 'value1',
  property2: 'value2',
  property3: 'value3'
};

const { property_1, property_2, property_3 } = object1;

console.log(property_1); // 'value1'
console.log(property_2); // 'value2'
console.log(property_3); // 'value3'

// Renaming Variables
// You can also rename variables while destructuring:

const object2 = {
  property1: 'value1',
  property2: 'value2'
};

const { property1: newProp1, property2: newProp2 } = object2;

console.log(newProp1); // 'value1'
console.log(newProp2); // 'value2'

// Default Values
// You can assign default values to variables in case 
// the property does not exist in the object:

const object3 = {
  property1: 'value1'
};

const { propert1, propert2 = 'default value' } = object3;

console.log(propert1); // 'value1'
console.log(propert2); // 'default value'

// Nested Objects
// You can also destructure nested objects:

const object4 = {
  property1: 'value1',
  nestedObject: {
    nestedProperty: 'nestedValue'
  }
};

const { propert_y1, nestedObject: { nestedProperty } } = object4;

console.log(propert_y1); // 'value1'
console.log(nestedProperty); // 'nestedValue'

// Using Rest Syntax
// You can use the rest syntax to collect 
// remaining properties into a new object:

const object5 = {
  property1: 'value1',
  property2: 'value2',
  property3: 'value3'
};

const { property1, ...rest } = object5;

console.log(property1); // 'value1'
console.log(rest); // { property2: 'value2', property3: 'value3' }
// Here, property1 is extracted, and the rest of the properties are collected into the rest object.


// Function Parameters
// Object destructuring can also be used directly in function parameters:

function display({ property1, property2 }) {
  console.log(property1);
  console.log(property2);
}

const object = {
  property1: 'value1',
  property2: 'value2'
};

display(object); // 'value1' 'value2'


//API-->

//json file format
// {
//     "name": "hitesh",
//     "coursename": "js in hindi",
//     "price": "free"
// }

[
    {},
    {},
    {}
]