const user={
    username:"ishu",
    price: 342534523,
    welcomeMessage: function(){
        console.log(`${this.username} ,welcoe to the website`);
        console.log(this); //will print the current context of this 'this'
    }
}

user.welcomeMessage()
user.username="shubham"
user.welcomeMessage()

console.log("\n",this); //o/p-> {}= empty  , in this workplace
//but the o/p will be diffferent if you run this command in the browser console
//(by inspecting)  op mayb be window ...etc

//********************************* */

function cup(){
    let userName="Ishu"
    console.log(this.userName); //o/o: undefined
    //this 'this' keyword is  used in object , no use in funcion
}

cup()

//arrow function-->
console.log("\n","arrow function-->");
const chai =()=>{
    let ussername="ishu"
    console.log(this); // o/p--> {},empty 
}
chai()

//1) basic syntax:
const add1 = (a, b) => {
  return a + b;
};

//2) Short Syntax with Implicit Return:
const add2 = (a, b) => a + b;

//3)Single Parameter:
const square = x => x * x;

//4)No Parameters:
const greet = () => 'Hello!';

//5) Returning Object Literals:
const getUser = () => ({ name: 'John', age: 30 });  //while returning of the object it must be enclosed in braces



//points to be noted:
//In object the value of the key can be function , nd the function can be arrow, regular etc

//6)context with this

const obj1 = {
  value: 10,
  getValue: () => this.value
};

console.log(obj1.getValue()); // undefined, because `this` is inherited from the global scope

const obj2 = {
  value: 10,
  getValue: function() {
    return this.value;
  }
};

console.log(obj2.getValue()); // 10, because `this` refers to the obj



