const name="ishu"
const repocount=50

// console.log(name+ repocount+"value");

console.log(`hello my name is ${name} and my repo count is ${repocount}`);
//this way of string declaration is like object
const gn=new String("gameIshu-gi") //another way of declaring the string 


//using different types of function in string-->


console.log(gn[0]);
console.log(gn.__proto__);  //to see the object 


console.log(gn.length); //8
console.log(gn.toUpperCase());
console.log(gn); //[String: 'gameIshu']

console.log(gn.charAt(2));  //m
console.log(gn.indexOf('m'));  //2

//slicing
const newString=gn.substring(0,4) //4th index not included
console.log(newString);

const anotherString=gn.slice(-8,4) 
console.log(anotherString); //o/p->e


const newStirng1="  ishu   "
console.log(newStirng1);
console.log(newStirng1.trim());  //removes extra spaces from end and the starting

const url="ttps://ushucom/ishu%20kanaw"

console.log(url.replace('%20','-')); //donot change the actual string


console.log(url.includes("Ishu")); //return true or false

console.log(gn.split('-')); //return a list of string 

