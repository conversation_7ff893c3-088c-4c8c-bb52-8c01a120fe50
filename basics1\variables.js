const accountId=1445
let accountEmail="<EMAIL>"
var accountPw="423" //used earlier, scope prb!!!
accountCity="Delhi" //not recommendable
let accountState //value=undefined
// accountId=2

/*
prefer not to use var
because of issue in block scope and functional scope
*/
accountEmail="dfgg.gmail.com"
accountPw="34"
accountCity="trhr"

console.log(accountId)
console.table([accountEmail,accountId,accountPw,accountCity,accountState])

