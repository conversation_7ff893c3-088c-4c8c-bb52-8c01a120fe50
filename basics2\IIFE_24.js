// Immediately Invoked Function Expressions (IIFE)
//purpose: IIFEs are a powerful tool in JavaScript for creating local scopes
//  and avoiding global variable pollution.
//disadvantage  is that it can be used only once

(function chai(){
    // named IIFE = regular function
    console.log(`DB CONNECTED`);
})();

//eg-1
( (name) => {
    //unamed IIFE=arrow function
    console.log(`DB CONNECTED TWO ${name}`);
} )('hitesh')

//eg-2
let result = (function(a, b) {
  return a + b;
})(5, 10);

console.log(result); // 15
