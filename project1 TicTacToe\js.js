let boxes=document.querySelectorAll(".box")
let resetBtn=document.querySelector("#reset")
let newGameBtn=document.querySelector("#newBtn")
let newGameBtnDraw=document.querySelector("#newBtnDraw")
let msgContainer=document.querySelector(".msg")
let msgContainerDraw=document.querySelector(".msgDraw")
let p=document.querySelector("#msg")

let turn0= true; //playerX,playerO

const winPatterns=[
    [0,1,2],
    [0,3,6],
    [0,4,8],
    [1,4,7],
    [2,5,8],
    [2,4,6],
    [3,4,5],
    [6,7,8]
]

const resetting=()=>{
    turn0=true;
    for(let box of boxes){
        box.innerText="";
        box.disabled=false;
    }
    msgContainer.classList.add("hide");
    msgContainerDraw.classList.add("hide");
}
newGameBtn.addEventListener("click",resetting)
resetBtn.addEventListener("click",resetting)
newGameBtnDraw.addEventListener("click",resetting)

let drawCount=0;
boxes.forEach((box)=>{
    box.addEventListener("click",()=>{
        console.log("box was clicked");
        drawCount++;
        if(turn0){
            box.classList.add("changeColor");
            box.innerText="O"
            turn0=!turn0;
        }
        else{
            box.classList.remove("changeColor");
            box.innerText="X"
            turn0=!turn0;
        }
        box.disabled=true; //only works on form elements like input,button,textArea etc 
        //the button is disabled, meaning the user cannot interact with it.
        // You can also use .disabled = false to enable the element again.
        
        checkWinner();
    })
})

const disabledBtn=()=>{
    for(box of boxes){
        box.disabled=true
    }
}
const showDraw=()=>{
    msgContainerDraw.classList.remove("hide");
    disabledBtn();
}
const showWinner=(winner)=>{
    p.innerText=`Congratulation, Winner is ${winner}`;
    msgContainer.classList.remove("hide")
    disabledBtn();
}
const checkWinner=()=>{
    for(let pattern of winPatterns){
        // console.log(pattern[0],pattern[1],pattern[2]); //wining pattern
        // console.log(boxes[pattern[0]].innerText,boxes[pattern[1]].innerText,boxes[pattern[2]].innerText); // boxes assciated with the index

        let pos1Val=boxes[pattern[0]].innerText
        let pos2Val=boxes[pattern[1]].innerText
        let pos3Val=boxes[pattern[2]].innerText
        
        if(pos1Val !="" && pos2Val!="" && pos3Val!=""){
            if(pos1Val===pos2Val && pos2Val==pos3Val){

                showWinner(pos1Val);

            }
            else if(drawCount==9){
                showDraw();
            }
        }

        
    }
}

