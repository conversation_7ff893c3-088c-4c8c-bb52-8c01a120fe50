<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>innerText vs textContent vs innerHTML</title>
    <style>
        .hidden { display: none; }
    </style>
</head>
<body>
    <div id="example">
        <p>This is a <span>simple</span> example.</p>
        <p class="hidden">This is hidden.</p>
    </div>

    <script>
        const exampleDiv = document.getElementById('example');

        // Using innerText
        const innerTextValue = exampleDiv.innerText;
        console.log('innerText:', innerTextValue);

        // Using textContent
        const textContentValue = exampleDiv.textContent;
        console.log('textContent:', textContentValue);

        // Using innerHTML
        const innerHTMLValue = exampleDiv.innerHTML;
        console.log('innerHTML:', innerHTMLValue);

        // Modifying the content
        exampleDiv.innerText = 'Modified using innerText';
        exampleDiv.textContent = 'Modified using textContent';
        exampleDiv.innerHTML = '<p>Modified using <strong>innerHTML</strong></p>';
    </script>
</body>
</html>
<!-- Explanation -->
<!-- innerText:

Retrieves or sets the text content of an element, but it only includes visible text. It will not include text from elements hidden with CSS (e.g., display: none).
It triggers a reflow of the page to compute the CSS, making it slower in some cases.
Useful when you want to get or set the visible text content of an element.
textContent:

Retrieves or sets the text content of an element, including all text within the element and its descendants, regardless of CSS visibility.
It is faster because it doesn't trigger a reflow to compute CSS styles.
Useful when you want to get or set the complete text content, ignoring CSS visibility.
innerHTML:

Retrieves or sets the HTML content of an element, including HTML tags and structures.
Useful when you need to get or set the HTML structure within an element, not just the text content.
It can introduce security risks such as XSS (Cross-Site Scripting) if user input is inserted directly into the HTML.
Differences Summary
Visibility: innerText only includes visible text, while textContent includes all text regardless of visibility.
Performance: textContent is generally faster as it doesn't trigger reflow.
Content Type: innerHTML handles HTML content (tags and all), whereas innerText and textContent only deal with plain text. -->
