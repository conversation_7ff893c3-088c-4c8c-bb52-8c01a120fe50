function sayMyName(){

}

function add(number1,number2){
   console.log( number1+ number2);
}

add(3,'7')
add(3,7)
add(3,"a")

function add2(number1,number2){
    return number1+number2
}
const result=add2(4,5)
console.log(result);

function loginUserMessage(userName){
    return `${userName} just logged in`
}

// (userName="sam") //default value

console.log(loginUserMessage()); //o/p->undefined just logged in
console.log(loginUserMessage('ishu'));

//rest parameters

function calcSum(...um1){
    return um1
}
console.log(calcSum(4,5,6));

//function argument as object
const user = {
    username: "hitesh",
    price: 199
}

function handleObject(anyobject){
    console.log(`Username is ${anyobject.username} and price is ${anyobject.price}`);
}
handleObject(user)

const myNewArray=[200,300,400,500]
function returnSecValue(getArray){
    return getArray[1]
}
console.log(returnSecValue(myNewArray));