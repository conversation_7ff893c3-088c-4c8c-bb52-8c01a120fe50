function getData(dataId){  //an eg of how an api sends promises after some time coz of lag 
    return new Promise((resolve,reject)=>{ //here we have created this lag problem
        setTimeout(()=>{  // in real life scenerio it is due to the api!!
            console.log("data :",dataId);
            resolve("success");
            // reject("some error occured");
            // if(getNextData){
            //     getNextData();
            // }
        },5000);
    })
}

//now solving by promise chaining instead of call back hell

// getData(1).then((res)=>{
//     console.log(res);
//     getData(2).then((res)=>{
//         console.log(res);
//     })
// })

//more professional way to write above is:
getData(1).then((res)=>{  //*****this res contains the result of promise, which is a message(or objector any data wich can be in any format-like json,xml,html then we use ceratain methods or functions to convert or extarct data from thus object in desdirable form) of resolve or reject
    console.log(res);
    return getData(2);
})
.then((res)=>{
    return getData(3);
})
.then((res)=>{
    console.log(res);
})

//we can also add catch with above in the end to handle the reject for all!!
