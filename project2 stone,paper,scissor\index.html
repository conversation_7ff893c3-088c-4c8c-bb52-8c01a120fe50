<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rock,scissor,paper game</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <h1>Rock paper Scissor</h1>

    <div class="choices">
        <div class="choice" id="paper"><img src="./images/paper.png" alt="paper"></div>
        <div class="choice" id="rock"><img src="./images/rock.png" alt="rock"></div>
        <div class="choice " id="scissor"><img src="./images/scissors.png" alt="scissor"></div>
    </div>

    <div class="scoreBoard">
        <div class="score">
            <p id="userScore">0</p>
            <p>You</p>
        </div>
            
        <div class="score">
            <p id="compScore">0</p>
            <p>Computer</p>

        </div>
    </div>

    <div class="msgContainer">
        <p id="msg">Play your Move</p>

    </div>
    <script src="js.js"></script>
</body>
</html>