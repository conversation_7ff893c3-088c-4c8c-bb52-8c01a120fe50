const marvel_heros = ["thor", "Ironman", "spiderman"]
const dc_heros = ["superman", "flash", "batman"]

marvel_heros.push(dc_heros) //array of array 

console.log(marvel_heros);
console.log(marvel_heros[3][1]);

const allHeros = marvel_heros.concat(dc_heros) //this method returns th new array
console.log(allHeros);

//alternate of concatnation : spreading!!
const all_new_heros = [...marvel_heros, ...dc_heros]

console.log(all_new_heros);

//nested arrayas to flattening the array
const another_array = [1, 2, 3, [4, 5, 6], 7, [6, 7, [4, 5]]] 

const real_another_array = another_array.flat(Infinity) //arguments=1,2,3...infinity denoting the depth
console.log(real_another_array);



console.log(Array.isArray("Hitesh")) //checkis if the agrument is an array or not->flase/true

//making arrays from other data types->

console.log(Array.from("Hitesh")) // makes the string into array
console.log(Array.from({name: "hitesh"})) // interesting : op->empty array
//in above converion to array we didn't specify whether to change the key or value to array thats y o/p is empty array
let score1 = 100
let score2 = 200
let score3 = 300
console.log(Array.of(score1, score2, score3));