//number-->
const score = 400
console.log(score);

const balance = new Number(100)
console.log(balance);

console.log(balance.toString().length);//converted to string then string inbuilt function is used
console.log(balance.toFixed(2));  //decimal precision upto 2

const otherNumber=2335.79524


console.log(otherNumber.toPrecision(7)); //2335.795
console.log(otherNumber.toPrecision(2)); //2.3e+3
console.log(otherNumber.toPrecision(5)); //2335.8

const hunderd=1000000
console.log(hunderd.toLocaleString());


//maths-->
console.log(Math);


console.log(Math.abs(-4));
console.log(Math.round(4.6));
console.log(Math.ceil(4.2));
console.log(Math.floor(4.9));
console.log(Math.min(4, 3, 6, 8));
console.log(Math.max(4, 3, 6, 8));


console.log(Math.random());  //return random value btw 0 and 1
console.log((Math.random()*10) + 1);
console.log(Math.floor(Math.random()*10) + 1);

const min = 10
const max = 20
//general formula for random number btw 10 and 20-->
console.log(Math.floor(Math.random() * (max - min + 1)) + min)










