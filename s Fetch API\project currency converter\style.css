*{
    margin: 0;
    padding: 0;
}
body{
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: aqua;
}
.container{
    background-color: white;
    padding: 2rem 2rem 1rem 2rem;
    border-radius: 1rem;
    min-height: 45vh;
    width: 50vh;

}
form{
    margin: 2rem 0 2rem 0;
}
form select,button,input{
    width: 100%;
    border: none;
    border-radius: 0.75rem;

}
form input{
    border: 1px solid grey;
    font-size: 1rem;
    height: 3rem;
    padding-left: 0.5rem;
}
.dropDown{
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 2rem 0 2rem 0;
    gap: 2rem;
}
.selectContainer img{
    max-width: 3rem;
}
.selectContainer{
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: blue;
    width: 6rem;
    border-radius: 0.5rem;
    border: 1px solid grey;
}
.dropDown i{
    font-size: 1.3rem;
    margin-top: 1.1rem;
}
.msg{
    margin: 2rem 0 2rem 0;
}
form button{
    height: 3rem;
    background-color: orange;
    color: aliceblue;
    font-size: 1.2rem;
    cursor:pointer;
}