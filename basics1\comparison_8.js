console.log(2 > 1);
console.log(2 >= 1);
console.log(2 < 1);
console.log(2 == 1);
console.log(2 != 1);

console.log("\n");  
//not recommendable to compare
console.log("2" > 1);
console.log("02" > 1);
console.log("\n");
console.log(null > 0); //false
console.log(null == 0); //false
console.log(null >= 0); //op->true
console.log("\n");
console.log(undefined == 0);  //all  false
console.log(undefined > 0);
console.log(undefined < 0);

// ===  : strict check (donot compare after conversion)
//just compare as it is- return false if datatype does'nt matches

console.log("\n");
console.log("2" === 2);