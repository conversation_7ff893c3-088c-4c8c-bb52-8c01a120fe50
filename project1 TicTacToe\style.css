* {
    margin: 0;
    padding: 0;
}
body{
    background-color: rgb(67, 62, 14);
    text-align: center;
    

}

.container{
    height: 70vh;
    /* width: 70vh; */
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
}
.game{
    height: 60vmin;
    width: 60vmin;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    gap: 1.5vmin;
}

.box{
    background-color: white;
    height: 18vmin ;
    width: 18vmin;
    border-radius: 10%;
    /* border: none; */
    box-shadow: 5px 5px 5px rgba(237, 238, 192,0.3) ;
    font-size: 15vmin;
    /* text-align: center; */

    color: rgb(124, 144, 130);
}

#reset{
    background-color: white;
    height: 50px;
    width: 150px;
    /* font-size: 30px; */
    display: flex;
    justify-content: center;
    align-items: center;
}
.button{
    display: flex;
    justify-content: center;
    font-size: 30px;
    /* align-items: center; */
}
#msg{
    color: azure;
    font-size: 40px;
}
.msg{
    height: 150px;
}
.msgDraw{
    height: 150px;
}
#msgDraw{
    color: azure;
    font-size: 40px;
}
.hide{
    display: none;
}

.changeColor{
    color: black;
}