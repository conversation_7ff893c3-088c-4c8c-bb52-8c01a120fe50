// function hello(){
//     console.log("hello");
// }


// console.log("one")
// console.log("two");

// setTimeout(hello,4000); //timeout (time in milli second)
// //we can also define it like arrow function in the setTimeout function itself
// //like --->

// setTimeout(()=>{
//     console.log("hello by arrow function");
// },4000);

// console.log("three"); //these will be immediately printed after the "two"
// console.log("four");

//callback-->
function sum(a,b){
    console.log(a+b);
}
function calc(a,b,sumCallback){
    sumCallback(a,b);
}
calc(1,2,sum); //call backs are passed without parathesis or call with the arrow function

const hello=()=>{

} 

function getData(dataId,getNextData){ //lets assume it is taking 2 sec to get data ffromn the api
    //2sec
    setTimeout(()=>{
        console.log("data",dataId);
        if(getNextData){
            getNextData();
        }
    },2000);
}

getData(1); //they all will be printed simultaneously at the same tine after 2 seconds
getData(2);
getData(3)// but we want that they prints in the interval of 2 seconds
//so we move to the callack Hell

//call-back hell
//somewhat related to the nested loops,recursion
// getData(1,()=>{
//     getData(2,()=>{
//         getData(3,()=>{
//             getData(4);
//         })
//     })
// })

///from here on go to the pomises .js to learn about promises and the chaining concept









