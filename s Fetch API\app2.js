//Axios
//sending  headers

// purpose-->
 //when the format of the data is different compare to the required one while fetcing data from api!!!
const url="https://icanhazdadjoke.com/";
async function getJokes(){
    try{
        const config={headers: {accept: "applcation/json"}}
        let res=await axios.get(url);
        console.log(res);
        console.log(res.data);//it is the in the html format! 
        //but we want info in application json format therefore we will use configuration
        
    }catch(err){
        console.log("error : ",err);

    }
}