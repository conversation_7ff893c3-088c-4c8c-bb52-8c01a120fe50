*{
    margin: 0;
    padding: 0;
    text-align: center;
}
h1{
    background-color: rgb(20, 69, 61);
    color: white;
    height: 2em;
    font-size:3em ;
    line-height: 1.5em;
    /* text-align: center; */
    /* padding-top: 15px; */
}
.choice{
    height: 165px;
    width: 165px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}
img{
    height: 150px;
    width: 150px;
    object-fit: cover ;
    border-radius: 50%;
}
.choices{
    display: flex;
    gap: 1.5em;
    justify-content: center;
    margin-top: 5em;
    margin-bottom: 5em;
}
.choice:hover{
    /* opacity: 0.5; */
    background-color: black;
    cursor: pointer;
}
.scoreBoard{
    display: flex;
    justify-content: center;
    /* align-items: center; */
    font-size: 2rem;
    gap: 5rem;
}

#userScore,#compScore{
    font-size: 4rem;
}
.msgContainer{
    margin-top: 5rem;
}
#msg{
    background-color: rgb(20, 69, 61);
    color: white;
    font-size: 2rem;
    display: inline;
    padding: 1em;
    border-radius: 1em;    
}