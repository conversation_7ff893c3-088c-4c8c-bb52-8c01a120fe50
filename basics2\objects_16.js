//singleton
const tu=new Object() //singleton object declaration,here the object is empty

//object literals-->>>>>>

const mySym= Symbol("key1") //using symbol as keys in object
const jsIshu=
{
    name: "ishu",  //by default keys are strings
    age: 18,
    [mySym]:"myKey1", //key as Symbol
    mySym:"mykey2",   //this is not a symmbol as key example but here the key is string
    location:"delhi",
}

//both displays same message
console.log(jsIshu.location)
console.log(jsIshu["location"]) //preferred

console.log(mySym,typeof mySym);
console.log(jsIshu[mySym],typeof jsIshu[mySym]);
console.log(jsIshu["mySym"],typeof jsIshu["mySym"]);

//updating the objects

jsIshu.location="mumbai"
console.log(jsIshu["location"]);

//making the object immune to changes-->
// Object.freeze(jsIshu)

//now even if we make the changes in the object they will
//not reflect to the object neither there will be any error

console.log(jsIshu);

//adding function to the object
jsIshu.greeting = function(){
    console.log("Hello JS user");
}
jsIshu.greetingTwo = function(){
    console.log(`Hello JS user, ${this.name}`);
}

console.log(jsIshu.greeting());
console.log(jsIshu.greetingTwo());
//o/p of above to sonsole lo op:
// Hello JS user
// undefined  //this 'undefined' will be explained later
// Hello JS user, ishu
// undefined
console.log(jsIshu);
