//the ways we have writen events in the html file are 
//known as the technique of the inline events 

//but this is the bad practise therefore we use the following format

//-->

// Node.event=() =>{
//     //handle here
// }

//note--> 
// 1)javascript event gets priority over inline event
//if they are overlapping
// 2) if we rewrite the event of the same element last event overrides the prvios all


let btn1=document.querySelector("#btn1");

// btn1.onclick= () => {
//     console.log("btn1 was clicked");
//     let a=25;
//     a++;
//     console.log(a); 
// }

// btn1.onclick= () => {
//     console.log("second event of the btn1"); 
//     //it overwrites the prev onclick event
// }

// btn1.onmouseover=()=>{  //it activates with the onclick event
//     console.log('diffenrent event other than on click on the same node');
// }

// btn1.onclick=(evt)=>{ //evt is the event object which has the details of the event
//     console.log(evt);
//     console.log(evt.type);
//     console.log(evt.target);
// }


// let div=document.querySelector("div")
// div.onmouseover=()=>{
//     console.log("you are inside the div ");
// }


//event listener-->


// node.addEventListener( event, callback )
// node.removeEventListener( event, callback )
// *Note : the callback reference should be same to remove

btn1.addEventListener("click",()=>{
    console.log("button1 was clicked-handler1");
})
//both these event execute at the same time unlike the prev method where it overwrites the prev one
btn1.addEventListener("click",()=>{
    console.log("button1 was cliccked-handler2");
})
const handler3=()=>{
    console.log("button1 was clicked-handler3");
}
btn1.addEventListener("click",handler3)
btn1.addEventListener("click",()=>{
    console.log("button1 was clicked-handler4");
})

btn1.removeEventListener("click",handler3) //here we have to store the function in handler3 so that 
//it passe  as a refernece otherwise if we had copy paste the function it would not have deleted the handler 3
//coz then it is not passed as a reference

//extracting form data-->
//use of .value instead of .innertext

let form=document.querySelector("form")

form.addEventListener("submit",function (e){
    e.preventDefault(); //to prevent form going to the /action url after submitting the form 
    // let inp=document.querySelector("input")
    // console.dir(inp);
    // console.log(inp.value); 
//     console.log(form)
//     console.dir(form) //it has element HTMLFormControlsCollects :
    
//     let user=document.querySelector("#user"); //or  use form.elements[0] it is better coz o/w we have to assign to each element of the form!!
//     let pass=document.querySelector("#pass");// or use form.elements[1] ==this.elements[] coz this refers to the object which is form here
//     console.log(user.value);
//     console.log(pass.vlaue);
})

//more events-->

// change event: the change event occures when the value of an element has been changed
// (only works on <inout>,<textarea>,and<select> elements)
let user=document.querySelector("#user");
user.addEventListener("change",function () {
    console.log("input changes-change");
    console.log("final vlaue : ",this.value);
    
    
});
//input vent: the event fires when the value of an <input>,<select> o <textarea> element has been changed
user.addEventListener("input",function () {
    console.log("input changes");
    console.log("final vlaue : ",this.value);
    
    
});

// Event bubbling is a concept in JavaScript where an event starts at the most specific element (the target element) and propagates up through the DOM tree to the root (document or window).

// For example, if you have an event listener attached to both a button and its parent container, when the button is clicked, the event first triggers on the button (the target) and then "bubbles up" to the parent container, and possibly to higher elements in the DOM, unless the event propagation is stopped.

// Here's a simple example:

// html
// Copy code
// <div id="parent">
//   <button id="child">Click Me!</button>
// </div>

// <script>
//   document.getElementById("parent").addEventListener("click", function() {
//     alert("Parent clicked!");
//   });

//   document.getElementById("child").addEventListener("click", function() {
//     alert("Child clicked!");
//   });
// </script>
// When you click the button:

// The "Child clicked!" alert appears first (because the button is the target).
// Then, the "Parent clicked!" alert appears, because the event bubbles up to the parent element.
// You can stop the bubbling by calling event.stopPropagation() inside the event handler:

// javascript
// Copy code
// document.getElementById("child").addEventListener("click", function(event) {
//   alert("Child clicked!");
//   event.stopPropagation(); // Stops the event from bubbling up
// });
// In this case, clicking the button will only trigger the "Child clicked!" alert, and the parent’s event handler will not be triggered.

 
