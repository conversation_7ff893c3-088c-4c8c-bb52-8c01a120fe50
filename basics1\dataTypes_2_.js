"use strict"; //treat all js code as newer version
 
// alert(3+3) we are using nodejs ,not browser

console.log(3+3) 
console.log("Ishu") 

let name="hitesh"
let age=18
let isLoggedIn=false

//bigint
//string=>""
// boolean=> true/false
//null => stand alone value
//undefined=> explained value

//symbol => unique
//object

console.log(typeof "stgb")
console.log(typeof 54)
console.log(typeof null) //op-> object
console.log(typeof undefined) //op-> undefined


