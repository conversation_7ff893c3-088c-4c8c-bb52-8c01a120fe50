// const url="https://cat-fact.herokuapp.com/facts"
// const factPara=document.querySelector("#fact")
// const btn=document.querySelector("#btn")


// //  by async await-->

// const getFacts=async ()=>{
//     let response =await fetch(url);
//     console.log(response);  //give in json format
//     console.log(response.status);  //we can print content or other imp features of the response
//     let data =await response.json();
//     // console.log(data);
//     // console.log(data[0]);
//     factPara.innerText=data[0].text;
// }


// // by call chaining -->

// // function getFacts(){
// //     fetch(url).then((response)=>{
// //         return response.json();
// //     })
// //     .then((data)=>{
// //         console.log(data);
// //         factPara.innerText=data[0].text;
// //     })
// // }
// btn.addEventListener("click",getFacts);

//*****upper content is the old one*****

//fetching data from 1)api using promise chaining-->

let url="https://catfact.ninja/fact";
// fetch(url) //always returns a promise
// .then((res)=>{
//     console.log(res);//*****important****** */
//      return res.json() //Parsing JSON is the process of converting a JSON-formatted string into a JavaScript object.
//     //  This allows your program to interact with the data in a structured and programmatic way.
//     //it always returns a promise
    
// })
// .then((data)=>{
//     console.log(data);
//     return fetch(url); //again fetching the data!!  -- using the chaining method!!
// })
// .then((res)=>{
//     return res.json();
// })
// .then((data)=>{
//     console.log(data);
// })
// .catch((err)=>{
//     console.log(err);
    
// })

//2)by  async and await->
// async function getFacts(){
//     try{
//         let res=await fetch(url);
//     console.log(res);
//     let data= await res.json();
//     console.log(data)
//     }
//     catch(err){
//         console.log("error: ",err);
//     }
//     console.log("bye");
    
// }   
// getFacts()

//3)using axios library to fetch data
//why axio mehtod is better than fetch cox fetch returse response not in our required format we need
//to parse it using json method!! but by axios method it returns directly in our required format!!
async function getFacts(){
    try{
        let res=await axios.get(url); //dirstly gives data in json-js object format 
        // console.log(res); //res is  essentially a dict so yeah you can use access data like this 
        // console.log(res.data.fact);
        return res.data.fact
        
    }
    catch(err){
        console.log("error: ",err);
        return "NO fact found";
    }
    console.log("bye");
}

//joining it with button:to sho the fact whne the button is clicked!!
let btn=document.querySelector("button")
btn.addEventListener("click",async ()=>{
    let fact=await getFacts(); //
    console.log(fact);
})
