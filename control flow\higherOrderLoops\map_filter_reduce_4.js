// const coding = ["js", "ruby", "java", "python", "cpp"]


// const values = coding.forEach( (item) => {
//     //console.log(item);
//     return item
// } )

// console.log(values);  //return nothing therefore the output is undefined ,even if we add return statment but it returns undefined


//Filter--->
//return the list of ele wh passes the condition!
const myNums = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]

//filter use to return !! , the prb thaT WE face in for each
// const newNums = myNums.filter( (num) => {
//     return num > 4
// } )
// console.log(newNums); //o/p->[ 5, 6, 7, 8, 9, 10 ] , those who are greater than 4 are stored in newNums as le of array

const newNums = []

myNums.forEach( (num) => {
    if (num > 4) {
        newNums.push(num)
    }
} )

console.log(newNums);


const books = [
    { title: 'Book One', genre: 'Fiction', publish: 1981, edition: 2004 },
    { title: 'Book Two', genre: 'Non-Fiction', publish: 1992, edition: 2008 },
    { title: 'Book Three', genre: 'History', publish: 1999, edition: 2007 },
    { title: 'Book Four', genre: 'Non-Fiction', publish: 1989, edition: 2010 },
    { title: 'Book Five', genre: 'Science', publish: 2009, edition: 2014 },
    { title: 'Book Six', genre: 'Fiction', publish: 1987, edition: 2010 },
    { title: 'Book Seven', genre: 'History', publish: 1986, edition: 1996 },
    { title: 'Book Eight', genre: 'Science', publish: 2011, edition: 2016 },
    { title: 'Book Nine', genre: 'Non-Fiction', publish: 1981, edition: 1989 },
  ];

  let userBooks = books.filter( (bk) => bk.genre === 'History')

  userBooks = books.filter( (bk) => { 
    return bk.publish >= 1995 && bk.genre === "History"
})
  console.log(userBooks);

//map-->

const myNumbers=[1,2,3,4,5,6,7,8,9,10]

const newNumbers=myNumbers.map((num)=> num+10)
console.log(newNumbers);

//same thing byb foreach method:
const newNumByForEach=[]
myNumbers.forEach( (num)=>{
    newNumByForEach.push(num+10)
})
console.log(newNumByForEach);

// Key Differences:
// Transformation vs. Filtering: map is used to transform each element of an array based on a provided function, while filter is used to select a subset of elements that meet a certain condition.
// Resulting Array Size: The resulting array from map will always have the same number of elements as the original array, while the resulting array from filter may have fewer elements, depending on how many elements pass the test.

// key usages:
// Use filter when you need to remove elements from an array based on a condition.
// Use map when you need to transform every element of an array.

//similarities between filter and map
// Both are higher-order functions.
// Both are array methods.
// Both do not modify the original array (immutable operations).
// Both iterate over each element of the array.
// Both use a callback function with a similar syntax.
// Both return a new array.


//interesting - chaining concept of functions

let n=myNumbers
                .map((num)=> num+10)
                .map((num)=> num+1)
                .filter((num)=> num>=40)

console.log(n);

//reduce--->

const myNums1 = [1, 2, 3]


//by normal function defination:

// const myTotal = myNums.reduce(function (acc, currval) {  //here acc is accumulator and currval is currentValue
//     console.log(`acc: ${acc} and currval: ${currval}`);
//     return acc + currval
// }, 0)  //here 0 is given to the accumulator, this is also used as syntax in arrow funtion when we use {}

//by arrow function
const myTotal = myNums1.reduce( (acc, curr) => acc+curr, 0)

console.log(myTotal);


const shoppingCart = [
    {
        itemName: "js course",
        price: 2999
    },
    {
        itemName: "py course",
        price: 999
    },
    {
        itemName: "mobile dev course",
        price: 5999
    },
    {
        itemName: "data science course",
        price: 12999
    },
]

const priceToPay = shoppingCart.reduce((acc, item) =>{
    return acc + item.price
},0)

console.log(priceToPay);

//various uses of reduce:

// 1. Summing an Array
const numbers = [1, 2, 3, 4, 5];
const sum = numbers.reduce((accumulator, currentValue) => accumulator + currentValue, 0);
console.log(sum); // Output: 15

// 2. Finding the Maximum Value
const max = numbers.reduce((accumulator, currentValue) => Math.max(accumulator, currentValue), numbers[0]);
console.log(max); // Output: 5

// 3. Flattening an Array of Arrays
const nestedArray = [[1, 2], [3, 4], [5]];
const flattenedArray = nestedArray.reduce((accumulator, currentValue) => accumulator.concat(currentValue), []);
console.log(flattenedArray); // Output: [1, 2, 3, 4, 5]

// 4. Removing Duplicates from an Array
const numbersDuplicate = [1, 2, 3, 4, 5, 5, 4, 3, 2, 1];
const uniqueNumbers = numbersDuplicate.reduce((accumulator, currentValue) => {
  if (!accumulator.includes(currentValue)) {
    accumulator.push(currentValue);
  }
  return accumulator;
}, []);
console.log(uniqueNumbers); // Output: [1, 2, 3, 4, 5]

// 5. Creating a Lookup Table
const users = [
  { id: 1, name: 'Alice' },
  { id: 2, name: 'Bob' },
  { id: 3, name: 'Charlie' }
];
const userLookup = users.reduce((accumulator, currentValue) => {
  accumulator[currentValue.id] = currentValue;
  return accumulator;
}, {});
console.log(userLookup);
// Output: { '1': { id: 1, name: 'Alice' }, '2': { id: 2, name: 'Bob' }, '3': { id: 3, name: 'Charlie' } }

// 6. Calculating Average
const average = numbers.reduce((accumulator, currentValue, index, array) => {
  accumulator += currentValue;
  if (index === array.length - 1) {
    return accumulator / array.length;
  }
  return accumulator;
}, 0);
console.log(average); // Output: 3


