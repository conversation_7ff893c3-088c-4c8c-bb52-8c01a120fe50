let div=document.querySelector("div");
console.log(div);

let id=div.getAttribute("id")
console.log(id);

let name=div.getAttribute("name");
console.log(name);

let para=document.querySelector("p")
console.log(para.getAttribute(("class")));

//setAttribute(attr,value) //to assignor change the existing one new attribute to the element /tag/id/class/div

para.setAttribute("class","p") //here the existing attribute is being changed
//to add multiple classes to the same element then we use classList so that all the properties ofthe
//classes apply to the element


//node style-->

//here now without the changing the html , we are able to make changes ion the webpage with the haelp of js
div.style.backgroundColor="red"
// div.innerText="Hello there"


//insert elements
let newButton=document.createElement("button")
newButton.innerText="click me";

console.log(newButton); //will not be visible on the page up till here
//we now have to add this buton on the DOM tree

div.append(newButton)//added in the end of the div block , does changing in that element unlike childppend

//div.prepend(newButton) //to add the button in the begining of the div b;ock
//div.before(newButton) //button willl be the added before the begining of the div block
div.after(newButton) //button will be the added after the ending of the div block

let newHead = document.createElement("h1");
newHead.innerHTML = "<b>hi, I'm Ishu</b>";
document.querySelector("body").prepend(newHead);


//delete element
let p=document.querySelector("p")
para.remove();

//read about appendChild() and removeChild()
//refer to mdn

// appendChild
// Purpose: To add a node to the end of the list of children of a specified parent node.

// Syntax:


// parentNode.appendChild(childNode);
// Parameters:

// parentNode: The parent node to which the child node will be appended.
// childNode: The node to append to the parent node.
// Returns: The appended child node.

// Example:


// let newElement = document.createElement('p');
// newElement.textContent = 'This is a new child element';
// let parentElement = document.getElementById('parent');
// parentElement.appendChild(newElement);
// Notes:

// The appendChild method moves the childNode from its current position (if it is already in the DOM) to the new position under parentNode.
// If childNode is already a child of parentNode, it is simply moved to the new position.
// removeChild
// Purpose: To remove a child node from the DOM and return the removed node.

// Syntax:

// javascript
// Copy code
// parentNode.removeChild(childNode);
// Parameters:

// parentNode: The parent node from which the child node will be removed.
// childNode: The node to remove from the parent node.
// Returns: The removed child node.

// Example:


// let parentElement = document.getElementById('parent');
// let childElement = document.getElementById('child');
// parentElement.removeChild(childElement);
// Notes:

// The removeChild method throws an error if the childNode is not a child of parentNode.
// It is possible to re-append the removed node to another parent or even the same parent, as it remains in memory until it is either appended again or discarded by the garbage collector.