//  Primitive

//7 typees : string,number,null,undefined,symbol,BigInt

const score=100
const scoreValue= 1003

const isLoggedIn=false
const outideTemp=null

let userEmail;

const id=Symbol('123')
const anotherId=Symbol('123')

console.log(id==anotherId) //false

const bigNumber=25346362457745763476567876867
console.log(typeof bigNumber)

// rfernence type(non primitive)

//arrays,objects,functions

const heros = ["shaktiman", "naagraj", "doga"];  //array
let myObj = {          //objects :similar to dictionary
    name: "hitesh",
    age: 22,
}

const myFunction = function(){ //variable method of function defination
    console.log("Hello world");
}

console.log(typeof anotherId); //op-> symbol
console.log(typeof myFunction); //op->fucntion
console.log(typeof outideTemp); //o/p-> object
console.log(typeof heros) //o/p-> object

// https://262.ecma-international.org/5.1/#sec-11.4.3

// Return type of variables in JavaScript
// 1) Primitive Datatypes
//        Number => number
//        String  => string
//        Boolean  => boolean
//        null  => object
//        undefined  =>  undefined
//        Symbol  =>  symbol
//        BigInt  =>  bigint

// 2) Non-primitive Datatypes
//        Arrays  =>  object
//        Function  =>  function
//        Object  =>  object


//************************************

//stack(primitive) -change by value ,heap(non-primitive) change by refernce(like list in python and array in c++)























