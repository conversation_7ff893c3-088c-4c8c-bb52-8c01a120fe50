// const baseUrl="https://cdn.jsdelivr.net/gh/fawazahmed0/currency-api@1/latest/currencies/usd/inr.json"
const baseUrl="https://cdn.jsdelivr.net/gh/fawazahmed0/currency-api@1/latest/currencies/usd/inr.json"
const dropDowns=document.querySelectorAll(".dropDown select")
const  btn=document.querySelector("form button")
const fromCurr=document.querySelector(".from select");
const toCurr=document.querySelector(".to select");
let msg=document.querySelector(".msg")

for(let select of dropDowns){
    for(let code in countryList){
        let newOption=document.createElement("option")
        newOption.innerText=code;
        newOption.value=code;
        //setting default 'from' and 'to' option
        if(select.name=="from" && code=="USD"){
            newOption.selected="selected";
        }
        if(select.name=="to" && code=="INR"){
            newOption.selected="selected";
        }
        select.append(newOption);

    }
    select.addEventListener("change",(evt)=>{
        updateFlag(evt.target); //The event.target property provides a reference to the element that dispatched the event.
    })
}

// const updateMsg()

const updateFlag=(element)=>{ //here this element is select
    let currcode=element.value;
    let countryCode=countryList[currcode];
    let newSrc=`https://flagsapi.com/${countryCode}/flat/64.png`;
    let oldimg=element.parentElement.querySelector("img");  //going to the parent element
    oldimg.src=newSrc;
    
}
btn.addEventListener("click",async (evt)=>{
    evt.preventDefault(); //coz in a form whenever we click on any button it automatically submits the form therefore to prevent these unnecessary events nd only allowing the declared one to happen we uses this
    let amount=document.querySelector(".amount input")
    let amtVal=amount.value;
    if(amtVal==="" || amtVal<1){
        amtVal=1;
        amount.value="1";
    }
    // console.log(fromCurr.value,toCurr.value);
    // const newurl=`${baseUrl}/${fromCurr.value.toLowerCase()}/${toCurr.value.toLowerCase()}.json`;
    const newurl=`${baseUrl}/${fromCurr.value.toLowerCase()}.json`;
    let response=await fetch(newurl);
    let data=await response.json();
    let rate=data[fromCurr.value.toLowerCase()][toCurr.value.toLowerCase()];
    
    let finalAmount=amtVal*rate;
    msg.innerText=`${amtVal} ${fromCurr.value}= ${finalAmount} ${toCurr.value}`

    // console.log(data);



})

