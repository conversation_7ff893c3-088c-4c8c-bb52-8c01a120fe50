<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link rel="stylesheet" href="style.css">
    
</head>
<body>
    <p class="content">Lorem ipsum dolor sit amet consectetur.</p>

    <script>
        let button=document.createElement("button")
        button.innerText="click me"
        document.querySelector("body").prepend(button)
        button.style.backgroundColor="red"
        button.style.color="white"
        button.style.height="100px"
        button.style.width="100px"

        let para=document.querySelector(".content")
        console.log(para.getAttribute("class"))
        // para.setAttribute("class","newClass") //it overwrite the existing class content
        //now by using classList
        para.classList.add("newClass") //now both class properties are being applied on the element


    </script>
</body>
</html>