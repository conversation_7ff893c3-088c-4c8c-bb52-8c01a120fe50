//arrays

const myArr=[0,1,2,3,4,5,true,'ishu'] //like in python

///another way of decaring arrray->
const myArr2 = new Array(1, 2, 3, 4)
// console.log(myArr[1]);

// Array methods-->

myArr.push(6)
myArr.push(7)
myArr.pop()
console.log(myArr);

myArr.unshift(9)  //places the 9 at the zeroth index of the array
console.log(myArr); //but this unshift causes all the elements of the array to shift their positions which results in time consumptions
myArr.shift() //revert back the shift of '9' ,make the array back to normal
myArr.shift() //shiftd the ele more to left , deleting 0
myArr.unshift(0)
console.log(myArr);


console.log(myArr.includes(3)); //return true/false whether the 9 is present or not
console.log(myArr.indexOf(3)); //returns the index


const newArr = myArr.join() //changes the datatype to string from array
console.log('\n');
console.log(myArr);
console.log(newArr); 


// slice, splice->


console.log("A ", myArr);

const myn1 = myArr.slice(1, 3) //3rd index not included

console.log(myn1);
console.log("B ", myArr); //no change in the original array


const myn2 = myArr.splice(1, 3) //3rd index gets included
console.log("C ", myArr);  // the splice part gets deleted from the original array
console.log(myn2);