// console.log('hello');
// // window.alert("hello")
// // window.console.log("hello")
// console.log(document.head)

let head=document.getElementById("heading1")
console.log(head);
console.dir(head)

let headings=document.getElementsByClassName("heading") //return collection of html objects if there are ,multiple objects with same classs name!! 
console.log(headings);
console.dir(headings)


let elements=document.querySelector("p") //only first ele
console.log(elements);
console.dir(elements);

let eleAll=document.querySelectorAll("p") //all elements
console.log(eleAll);
console.dir(eleAll);

let button=document.querySelector("#myId")
console.log(button);
console.dir(button)


// returns tag for element nodes
console.log(button.tagName)

//innertext
