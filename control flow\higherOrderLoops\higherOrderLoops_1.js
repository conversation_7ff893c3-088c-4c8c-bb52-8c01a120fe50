//for of

const arr=[1,2,3,4,5,6]

for(const num of arr){  //its like we iterate in maps or heap in c++ using stl methods
    console.log(num);
}
const greetings="hello there"
for( const greet of greetings){
    console.log(`Each char is ${greet}`);
}

//maps
const map= new Map()
map.set("In","India")
map.set("fn","france")
map.set("In","India") //contains only unique values, this will not get printed
map.set("fn","france")
console.log(map);

for(const key of map){
    console.log(key); //prints lists as pairs of key value->
    // [ 'In', 'India' ]
    // [ 'fn', 'france' ]


}


for(const [key,value] of map){
    console.log(key,"->",value);    
}

const myObject = {
    game1: 'NFS',
    game2: 'Spiderman'
}

// for (const [key, value] of myObject) { //error
//     console.log(key, ':-', value);
    
// }

// for (const val of myObject) { //error
//     console.log(val);
    
// }

