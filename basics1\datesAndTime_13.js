//Dates-->

let myDate=new Date()
console.log(myDate);
console.log(myDate.toString());
console.log(myDate.toDateString());
console.log(myDate.toLocaleString());
console.log(typeof myDate);  //op-> object

let myCreatedDate = new Date(2023, 0, 23)  //(year,month(startfrom 0),(date))
let myCreatedDate1 = new Date(2023, 0, 23, 5, 3) //(..,..,..,hour,minutes)
let myCreatedDate2 = new Date("2023-01-14") // 14/1/2023, 5:30:00 am
let myCreatedDate3 = new Date("01-14-2023") // 14/1/2023, 12:00:00 am
console.log(myCreatedDate.toLocaleString());
console.log(myCreatedDate1.toLocaleString());
console.log(myCreatedDate2.toLocaleString());
console.log(myCreatedDate3.toLocaleString());

let myTimeStamp=Date.now()
console.log(myTimeStamp); //op->time from 1jan 1970 in milli second
console.log(myCreatedDate.getTime()); //op in milliseconf fro 1jan 1970 to the mycreateDate

let newDate= new Date()
console.log(newDate.getMonth()+1);


//most frequently use fucntion
//use of toLocalString with date


//1) custom date format-->

let date=new Date()
let options = { year: 'numeric', month: 'long', day: 'numeric' };

// US English locale
console.log(date.toLocaleString('en-US', options));  // e.g., "June 3, 2024"

// German locale
console.log(date.toLocaleString('de-DE', options));  // e.g., "3. Juni 2024"

// Japanese locale
console.log(date.toLocaleString('ja-JP', options));  // e.g., "2024年6月3日"


//2) including weekday
let optionsWithWeekday = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };

// US English locale
console.log(date.toLocaleString('en-US', optionsWithWeekday));  // e.g., "Monday, June 3, 2024"

// German locale
console.log(date.toLocaleString('de-DE', optionsWithWeekday));  // e.g., "Montag, 3. Juni 2024"

// Japanese locale
console.log(date.toLocaleString('ja-JP', optionsWithWeekday));  // e.g., "2024年6月3日月曜日"


//3)Adding Time
let optionsWithTime = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: 'numeric',
    minute: 'numeric',
    second: 'numeric',
    timeZoneName: 'short'
};
  
// US English locale
console.log(date.toLocaleString('en-US', optionsWithTime));  // e.g., "June 3, 2024, 10:45:30 AM PDT"
  
// German locale
console.log(date.toLocaleString('de-DE', optionsWithTime));  // e.g., "3. Juni 2024 um 10:45:30 PDT"
  
// Japanese locale
console.log(date.toLocaleString('ja-JP', optionsWithTime));  // e.g., "2024年6月3日 10:45:30 JST"
  
