let b=document.querySelector("button")
let inp=document.querySelector("input")
let list=document.querySelector("ul")


b.addEventListener("click",function(){
    let li=document.createElement("li")
    li.innerText=inp.value;

    let d=document.createElement("button")
    d.innerText="delete";
    d.classList.add("del");
    li.appendChild(d);

    list.append(li);
    inp.value="";
});

//event delegation-->
// let delbutton=document.querySelectorAll(".del")
// for(db of delbutton){  // this  deleting event will only work on the existing li not on the tasks delete button which we can add!!
//     db.addEventListener("click",function(){  // this is known as event delegation!!
//         let p=this.parentElement;
//         p.remove();
//     });
// }
//so instead we will add this event listner to its parent!!
list.addEventListener("click",function(event){
    if(event.target.nodeName=="BUTTON"){ //why checking? o/w even if we clcik the on the task ,not on the button it will delete the task 
        let listitem=event.target.parentElement;  // will ensure dletion of task only when the delete button of task is clicked!!
        listitem.remove();
    }
});

//by chatgpt explanatin for event delegation:
// Event Delegation
//******* */ Event delegation is a technique in JavaScript where a parent element is used to handle events for its child elements, even if the child elements are dynamically added to the DOM. This is made possible by the concept of event bubbling.*********

// How Event Delegation Works
// Event Bubbling: When an event is triggered on a child element, it "bubbles up" to its parent and ancestor elements unless explicitly stopped.
// Using a Common Parent: Instead of adding event listeners to each child element, a single event listener is added to their parent. The listener then detects the event and determines which child element triggered it.
// Benefits of Event Delegation
// Performance: Reduces memory usage by attaching fewer event listeners.
// Dynamic Elements: Easily handles events for elements added dynamically to the DOM.
// Example: Without Event Delegation
// javascript
// Copy code
// document.getElementById('btn1').addEventListener('click', () => {
//     console.log('Button 1 clicked');
// });
// document.getElementById('btn2').addEventListener('click', () => {
//     console.log('Button 2 clicked');
// });
// Example: With Event Delegation
// javascript
// Copy code
// document.getElementById('buttonContainer').addEventListener('click', (event) => {
//     if (event.target.tagName === 'BUTTON') { 
//         console.log(`${event.target.textContent} clicked`);
//     }
// });
// Here, instead of adding event listeners to individual buttons, we added a single listener to their parent container (buttonContainer).

// Key Points
// Use the event.target property to identify the element that triggered the event.
// Use tagName, classList, or attributes (like data-*) to match the specific child element.
// Be cautious of unintended matches—apply checks to ensure only relevant elements respond to the event.