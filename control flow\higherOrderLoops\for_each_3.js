//for each-->
const coding = ["js", "ruby", "java", "python", "cpp"]

// coding.forEach( function (val){  //without name function is used, this function 
//     console.log(val);            //this function is excecuted on each ele of array
// } )

// coding.forEach( (item) => {  // we can also use arrow function instead of normal function
//     console.log(item);
// } )

// function printMe(item){   // we can also pass an existing function into it 
//     console.log(item);
// }

// coding.forEach(printMe)  // but remember to pass its refernece not the fucntion

coding.forEach( (item, index, arr)=> {  // also , keep note that it not only takes value of array as it parameter
    console.log(item, index, arr);       // but can also take its index and the whole array
} )

const myCoding = [
    {
        languageName: "javascript",
        languageFileName: "js"
    },
    {
        languageName: "java",
        languageFileName: "java"
    },
    {
        languageName: "python",
        languageFileName: "py"
    },
]

myCoding.forEach( (item) => {
    
    console.log(item.languageName);
} )
