let promise=new Promise((resolve,reject)=>{
    console.log("I'm a promise"); //state will pe pending now!!
    // resolve("Issue solved !"); 
    // reject("error occured")
})

function getData(dataId,getNextData){  //an eg of how an api sends promises after some time coz of lag 
    return new Promise((resolve,reject)=>{ //here we have created this lag problem
        setTimeout(()=>{  // in real life scenerio it is due to the api!!
            // console.log("data :",dataId);
            // resolve("success");
            reject("some error occured");
            if(getNextData){
                getNextData();
            }
        },5000);
    })
}



//handling the promises-->

// const getPromise=()=>{
//     return new Promise((resolve,reject)=>{
//         console.log("I am a promise");
//         resolve("success");
//         // reject("unsuccessfull")
//     }) 
// }
// let prm=getPromise();

// prm.then((res)=>{
//     console.log("promise fulfilled",res); //op--> promise fulfilled success
// })
// prm.catch((err)=>{
//     console.log("rejected",err); //op->rejected unsuccessfull
// })

//concept of chaining--->

function asynFunction1(){
    return new Promise((resolve,reject)=>{
        setTimeout(()=>{
            console.log("some data1");
            resolve("success")
        },4000)
    })
}
// let p1=asynFunction1();
// console.log("fetching data1....");
// p1.then((res)=>{
//     console.log(res);
// })

function asynFunction2(){
    return new Promise((resolve,reject)=>{
        setTimeout(()=>{
            console.log("some data2");
            resolve("success")
        },6000)
    })
}

// let p2=asynFunction2();
// console.log("fetching data2....");
// p2.then((res)=>{
//     console.log(res);

// })

let p1=asynFunction1();  
console.log("fetching data1....");
p1.then((res)=>{ //or asynFunction1.then...
    console.log(res);
    console.log("fetching data2....");
    let p2=asynFunction2();  //chaining concept
    p2.then((res)=>{
        console.log(res);
    })
})






