const student={
    fullName: "ishu",
    marks: 97.2,
    printMarks :function(){
        console.log("marks = ",this.marks);
    },
    printMarks2(){  //both these methods of declaring the function in the object are correct
        console.log("my marks!!");
    }
}

const employee={
    
    calcTax(){
        console.log("tax is great");
    }
}
//using __proto__ to set protoype to another object 
//by doing so student can now access all the funcions of the employee
//coz now all the function of the employee are added in the prototype section of the student
student.__proto__=employee

//classes-->
class tata{
    // constructor(){
    //     console.log("constructor is being called");
    //     //will be called automatically when we create an object
    // }
    //unlike other languages , in js only one constructor is allowed at a time
    constructor(brand,n){
        this.branding=brand;
        this.number=n;
    }
    start(){
        console.log("starting");
    }
    stop(){
        console.log("stop");
    }

    setBrand(brand){ 
        this.brandName=brand; 
        //will set a brandName as a var for the object for which it is being called
    }
}
let fortuner = new tata();
fortuner.setBrand("fortuner")
let lexex= new tata();
lexex.setBrand("lex")
let newobject=new tata("bexux",777);

console.log(fortuner);
console.log(lexex);
console.log(newobject);

//inheritance

class parent{
    hello(){
        console.log("hello");
    }
}
class child extends parent{

}
let obj=new child();
obj.hello();

//super keyword

class person{
    constructor(){
        this.species="homo spaiens";
    }

    eat(){
        console.log("eat");
    }
}

class engineer extends person{
    constructor(branch){
        super(); //invoking the parent class constructor 
        //otherwise it will give an error
        this.branch=branch;
    }
    work(){
        console.log("solve problem, build something");
    }

}
let engObj=new engineer("Information engineer");