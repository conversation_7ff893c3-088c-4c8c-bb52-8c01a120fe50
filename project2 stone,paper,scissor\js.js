let userScore=0;
let compScore=0;
const msg=document.querySelector("#msg")
const choices=document.querySelectorAll(".choice")
const pUser=document.querySelector("#userScore")
const pComp=document.querySelector("#compScore")



choices.forEach((choice) => {
    console.log(choice);
    choice.addEventListener("click",()=>{
        const userChoice=choice.getAttribute("id")
        console.log("choice was clicked",userChoice);
        playGame(userChoice);

    })
});
const genCompChoice=()=>{
    // rock ,paper,scissor
    const option=["rock","paper","scissor"]
    const randIdx= Math.floor(Math.random()*3);
    return option[randIdx];

}
const drawGame=()=>{
    console.log("Game was draw");
    msg.innerText="Draw,Play Again"
    msg.style.backgroundColor="grey";
}
const playGame=(userChoice)=>{
    console.log("user choice = ",userChoice);
    //generate computer choice
    const compChoice=genCompChoice();

    console.log("comp choice : ",compChoice);

    if(userChoice===compChoice){
        drawGame();
    }else{
        let userWin=true;
        if(userChoice==="rock"){
            //siccsor,paper
            userWin=compChoice==="paper"? false:true;

        }
        else if(userChoice==="paper"){
            //rock,scissor
            userWin=compChoice==="scissor"? false:true;
        }
        else{ //scissor-user
            //rock,paper
            userWin=compChoice=="rock"?false:true;
        }

        showWinner(userWin,userChoice,compChoice);

    }
}
showWinner=(userWin,userChoice,compChoice)=>{
    if(userWin) {
        userScore++;
        console.log("You win");
        msg.innerText= `You win, ${userChoice} beats ${compChoice}`;
        msg.style.backgroundColor="green";
        pUser.innerText=userScore;
    }
    else{
        compScore++;
        console.log("You loose");
        msg.innerText=`You loose,${compChoice} beats ${userChoice}`
        msg.style.backgroundColor="red";
        pComp.innerText=compScore;
    }
}
