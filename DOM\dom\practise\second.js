let divs=document.querySelectorAll(".box")
console.log(divs);
console.log(divs.length)

//we can go and change individuall using inner functions
console.log(divs[0]);
console.log(divs[1]);
console.log(divs[2]);

divs[0].textContent="new unique value: 1"
divs[1].textContent="new unique value: 2"
divs[2].textContent="new unique value: 3"

//using for of loop

let idx=0;
for(div of divs){
    const userInput = prompt(`Enter content of ${idx+1} div : `);
    div.textContent=userInput;
    idx++;
}

