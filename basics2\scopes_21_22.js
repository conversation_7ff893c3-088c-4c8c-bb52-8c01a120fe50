

if(true){
    let a=10
    const b=20;
    var c=30
}
// console.log(a); //errror
// console.log(b); //error
console.log(c); //but this will print 30 , scope prb thats y in lect 1 we 
//told not to use var method of declaring the variable

//scope concept of global and local is same as of python


//nested functions scope-->

function one(){
    const username = "hitesh"

    function two(){
        const website = "youtube"
        console.log(username);
    }
    // console.log(website); // error coz website is out of scope

    two()

}

// one()

if (true) {
    const username = "hitesh"
    if (username === "hitesh") {
        const website = " youtube"
        // console.log(username + website);
    }
    // console.log(website); error
}

// console.log(username); error


// ++++++++++++++++++ interesting ++++++++++++++++++


console.log(addone(5)) //this works

function addone(num){
    return num + 1
}



addTwo(5)  //this causes error 
const addTwo = function(num){  //variable function example
    return num + 2
}