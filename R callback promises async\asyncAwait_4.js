// function api(){
//     return new Promise((resolve,reject)=>{
//         setTimeout(()=>{
//             console.log("weather data");
//             resolve(200);
//         },2000)
//     })
// }
// async function getWeatherData(){
//     await api(); //1st
//     await api(); //2nd after 2 sec of 1st execution
// }

function getData(dataId){  //an eg of how an api sends promises after some time coz of lag 
    return new Promise((resolve,reject)=>{ //here we have created this lag problem
        setTimeout(()=>{  // in real life scenerio it is due to the api!!
            console.log("data :",dataId);
            resolve("success");
            // reject("some error occured");
            // if(getNextData){
            //     getNextData();
            // }
        },2000);
    })
}
async function getAllData(){
    console.log("fetching data1....");
    await getData(1);
    console.log("fetching data2....");
    await getData(2);
    console.log("fetching data3....");
    await getData(3);
    console.log("fetching data4....");
    await getData(4);
    console.log("fetching data5....");
    await getData(5);
    console.log("fetching data6....");
    await getData(6);

    //what is await function gives rejected promise then the execution will stop
    // but if we have some other tasks to perform after async function they will not get execute so
    // we use try and catch all async function it try block!!
}
//here we will make use of IIFE coz on console we first need to call the async fucntion first
//to fetch all the data
// !!

//async function can also be used with .then(as discussed in promises)
//  as async function are simply function with default promise!